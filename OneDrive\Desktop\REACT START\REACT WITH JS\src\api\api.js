// Main API exports
export * from "./functions";
export { default as apiClient } from "./apiConfig";

// API endpoints constants
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    LOGIN: "/auth/login",
    REGISTER: "/auth/register",
    LOGOUT: "/auth/logout",
    ME: "/auth/me",
    REFRESH: "/auth/refresh",
    FORGOT_PASSWORD: "/auth/forgot-password",
    RESET_PASSWORD: "/auth/reset-password",
    CHANGE_PASSWORD: "/auth/change-password",
  },

  // User endpoints
  USERS: {
    BASE: "/users",
    BY_ID: (id) => `/users/${id}`,
    STATUS: (id) => `/users/${id}/status`,
    ROLE: (id) => `/users/${id}/role`,
    SEARCH: "/users/search",
    STATS: "/users/stats",
    BULK: "/users/bulk",
    EXPORT: "/users/export",
  },

  // Reports endpoints
  REPORTS: {
    DASHBOARD: "/reports/dashboard",
    USER_ACTIVITY: "/reports/user-activity",
    SYSTEM_PERFORMANCE: "/reports/system-performance",
    CUSTOM: "/reports/custom",
    TEMPLATES: "/reports/templates",
    GENERATE: "/reports/generate",
    DOWNLOAD: (id) => `/reports/${id}/download`,
    HISTORY: "/reports/history",
    SCHEDULE: "/reports/schedule",
    SCHEDULED: "/reports/scheduled",
  },

  // Settings endpoints
  SETTINGS: {
    BASE: "/settings",
    CATEGORY: (category) => `/settings/category/${category}`,
    RESET: "/settings/reset",
    PREFERENCES: "/settings/preferences",
    SYSTEM: "/settings/system",
    NOTIFICATIONS: "/settings/notifications",
    SECURITY: "/settings/security",
  },
};

// HTTP status codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
};

// API response types
export const API_RESPONSE_TYPES = {
  JSON: "application/json",
  BLOB: "blob",
  TEXT: "text",
  FORM_DATA: "multipart/form-data",
};
