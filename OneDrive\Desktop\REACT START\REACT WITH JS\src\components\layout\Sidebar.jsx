import { useSelector } from "react-redux";
import { NavLink } from "react-router-dom";
import {
  Home,
  Users,
  UserPlus,
  BarChart3,
  Settings,
  X,
  Shield,
  Briefcase,
  User,
} from "lucide-react";

const Sidebar = ({ isOpen, onClose }) => {
  const { user } = useSelector((state) => state.auth);

  const getNavigationItems = () => {
    const baseItems = [{ path: "/dashboard", icon: Home, label: "Dashboard" }];

    const roleBasedItems = {
      admin: [
        { path: "/users/create", icon: UserPlus, label: "Create User" },
        { path: "/users/list", icon: Users, label: "User Management" },
        { path: "/reports", icon: BarChart3, label: "Reports" },
        { path: "/settings", icon: Settings, label: "Settings" },
      ],
      manager: [
        { path: "/users/list", icon: Users, label: "View Users" },
        { path: "/reports", icon: BarChart3, label: "Reports" },
        { path: "/settings", icon: Settings, label: "Settings" },
      ],
      user: [{ path: "/settings", icon: Settings, label: "Settings" }],
    };

    return [...baseItems, ...(roleBasedItems[user?.role] || [])];
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case "admin":
        return Shield;
      case "manager":
        return Briefcase;
      case "user":
        return User;
      default:
        return User;
    }
  };

  const getRoleTheme = (role) => {
    switch (role) {
      case "admin":
        return {
          background: "from-black-600 to-blue-800",
          accent: "bg-blue-500",
          hover: "hover:bg-blue-700",
        };
      case "manager":
        return {
          background: "from-black to-purple-800",
          accent: "bg-purple-500",
          hover: "hover:bg-purple-700",
        };
      case "user":
        return {
          background: "from-green-600 to-green-800",
          accent: "bg-green-500",
          hover: "hover:bg-green-700",
        };
      default:
        return {
          background: "from-gray-600 to-gray-800",
          accent: "bg-gray-500",
          hover: "hover:bg-gray-700",
        };
    }
  };

  const theme = getRoleTheme(user?.role);
  const RoleIcon = getRoleIcon(user?.role);
  const navigationItems = getNavigationItems();

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        className={`
        fixed top-16 left-0 h-full bg-gradient-to-b ${
          theme.background
        } text-white 
        transform transition-transform duration-300 ease-in-out z-40
        ${isOpen ? "translate-x-0" : "-translate-x-full"}
        w-64
      `}
      >
        <div className="p-6">
          {/* User Role Section */}
          <div className={`${theme.accent} rounded-lg p-4 mb-6`}>
            <div className="flex items-center space-x-3">
              <RoleIcon className="w-8 h-8" />
              <div>
                <p className="font-semibold text-white">{user?.name}</p>
                <p className="text-xs text-white/80 capitalize">
                  {user?.role} Access
                </p>
              </div>
            </div>
          </div>

          {/* Close button for mobile */}
          <button
            onClick={onClose}
            className="lg:hidden absolute top-4 right-4 text-white hover:bg-white/10 p-1 rounded"
          >
            <X className="w-5 h-5" />
          </button>

          {/* Navigation */}
          <nav className="space-y-2">
            {navigationItems.map((item) => (
              <NavLink
                key={item.path}
                to={item.path}
                className={({ isActive }) => `
                  flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200
                  ${
                    isActive
                      ? "bg-white/20 text-white font-semibold"
                      : `text-white/80 ${theme.hover} hover:text-white`
                  }
                `}
                onClick={onClose}
              >
                <item.icon className="w-5 h-5" />
                <span>{item.label}</span>
              </NavLink>
            ))}
          </nav>
        </div>
      </div>
    </>
  );
};

export default Sidebar;
