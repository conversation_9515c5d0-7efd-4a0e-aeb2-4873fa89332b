{"hash": "a5e3b578", "configHash": "4c639939", "lockfileHash": "52bb1578", "browserHash": "153772b3", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "cb2b1c4c", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "6f1c62dc", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4efb4b24", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "a11d993f", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "06288659", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "ead06aab", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "47a76ab5", "needsInterop": false}}, "chunks": {"chunk-PBXWVHXV": {"file": "chunk-PBXWVHXV.js"}, "chunk-4HAMFFQC": {"file": "chunk-4HAMFFQC.js"}, "chunk-EQCVQC35": {"file": "chunk-EQCVQC35.js"}}}