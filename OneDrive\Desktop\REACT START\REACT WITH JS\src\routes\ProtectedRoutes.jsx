import { Routes, Route } from "react-router-dom";
import { lazy, Suspense } from "react";
import { useSelector } from "react-redux";
import Loader from "../components/common/Loader";

// Lazy load protected components
const Layout = lazy(() => import("../components/layout/Layout"));
const AdminDashboard = lazy(() => import("../pages/dashboard/AdminDashboard"));
const UserDashboard = lazy(() => import("../pages/dashboard/UserDashboard"));

const CreateUser = lazy(() => import("../pages/forms/CreateUser"));
const UserList = lazy(() => import("../pages/tables/UserList"));
const Reports = lazy(() => import("../pages/tables/Reports"));
const Settings = lazy(() => import("../pages/forms/Settings"));

function ProtectedRoutes() {
  const { user } = useSelector((state) => state.auth);

  const getDashboardComponent = () => {
    switch (user?.role) {
      case "admin":
        return AdminDashboard;
      case "user":
        return UserDashboard;
      default:
        return UserDashboard;
    }
  };

  return (
    <Suspense fallback={<Loader />}>
      <Routes>
        <Route path="/" element={<Layout />}>
          <Route path="dashboard" Component={getDashboardComponent()} />
          <Route path="users/create" element={<CreateUser />} />
          <Route path="users/list" element={<UserList />} />
          <Route path="reports" element={<Reports />} />
          <Route path="settings" element={<Settings />} />
        </Route>
      </Routes>
    </Suspense>
  );
}

export default ProtectedRoutes;
