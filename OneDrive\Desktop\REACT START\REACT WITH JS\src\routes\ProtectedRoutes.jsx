import { Routes, Route } from 'react-router-dom';
import { useSelector } from 'react-redux';
import Layout from '../components/layout/Layout';
import AdminDashboard from '../pages/dashboard/AdminDashboard';
import ManagerDashboard from '../pages/dashboard/ManagerDashboard';
import UserDashboard from '../pages/dashboard/UserDashboard';
import CreateUser from '../pages/forms/CreateUser';
import UserList from '../pages/tables/UserList';
import Reports from '../pages/tables/Reports';
import Settings from '../pages/forms/Settings';

function ProtectedRoutes() {
  const { user } = useSelector((state) => state.auth);

  const getDashboardComponent = () => {
    switch (user?.role) {
      case 'admin':
        return AdminDashboard;
      case 'manager':
        return ManagerDashboard;
      case 'user':
        return UserDashboard;
      default:
        return UserDashboard;
    }
  };

  return (
    <Routes>
      <Route path="/" element={<Layout />}>
        <Route path="dashboard" Component={getDashboardComponent()} />
        <Route path="users/create" element={<CreateUser />} />
        <Route path="users/list" element={<UserList />} />
        <Route path="reports" element={<Reports />} />
        <Route path="settings" element={<Settings />} />
      </Route>
    </Routes>
  );
}

export default ProtectedRoutes;
